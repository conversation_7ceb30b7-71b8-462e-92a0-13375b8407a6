# Linux_Plan_Min 配置文件
# 动态任务图谱差分更新实验配置

# 基本配置
seed: 42
task: cereals

# 数据路径配置
# 注意：由于数据集存储在远程服务器，本地测试时这些路径可能不存在
data_root: /data2/syd_data/Breakfast_Data
output_dir: ${oc.env:OUTPUT_DIR,./outputs}  # 使用环境变量或默认路径
stats_dir: ./stats

# 模型参数
model:
  # 特征维度 - 必须根据实际特征文件确认
  # Fisher: 4096, I3D: 2048, 其他可能是64等
  D: 64 
  M: 48        # cereals任务的动作类别数
  H: 256       # MLP隐藏层维度
  alpha: 0.05  # 权重调整的缩放因子
  lr: 1e-4     # 学习率

# 训练参数
trainer:
  max_epochs: 50
  batch_size: 16
  num_workers: 4
  accelerator: 'auto'  # 自动选择GPU或CPU
  devices: 1
  
  # 可选的训练配置
  gradient_clip_val: 1.0
  accumulate_grad_batches: 1
  
  # 验证配置
  val_check_interval: 1.0  # 每个epoch验证一次
  check_val_every_n_epoch: 1
  
  # 早停配置（可选）
  # patience: 10
  # monitor: val_loss
  # mode: min

# 数据配置
data:
  # 数据预处理参数
  normalize: false
  augmentation: false
  
  # 序列处理参数
  min_sequence_length: 2
  max_sequence_length: 1000

# 日志配置
logging:
  log_every_n_steps: 50
  save_top_k: 3
  monitor: val_iou
  mode: max

# 实验配置
experiment:
  name: cereals_dynamic_graph
  version: null  # 自动生成版本号
  tags: ["cereals", "dynamic_graph", "breakfast"]

# 调试配置
debug:
  fast_dev_run: false  # 快速开发运行（仅运行几个batch）
  overfit_batches: 0   # 过拟合测试（0表示不使用）
  limit_train_batches: 1.0  # 限制训练数据比例
  limit_val_batches: 1.0    # 限制验证数据比例

# Hydra配置
hydra:
  run:
    dir: ${output_dir}/hydra_runs/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: true
