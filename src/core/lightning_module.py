import pytorch_lightning as pl
import torch
import torch.nn.functional as F
from typing import List, Dict, Any, Optional


class TrainerModule(pl.LightningModule):
    """
    PyTorch Lightning训练模块

    整合差分MLP和任务图，实现完整的训练和验证流程
    """

    def __init__(self, diff_mlp, task_graph, learning_rate: float = 1e-4):
        """
        Args:
            diff_mlp: 差分更新MLP网络
            task_graph: 任务图模块
            learning_rate: 学习率
        """
        super().__init__()

        self.diff_mlp = diff_mlp
        self.g = task_graph
        self.lr = learning_rate

        # 存储验证步骤的输出
        self.validation_step_outputs = []

        # 保存超参数（忽略模型对象）
        self.save_hyperparameters(ignore=['diff_mlp', 'task_graph'])

    def training_step(self, batch, batch_idx):
        """训练步骤"""
        V_padded, y_padded, lengths = batch
        
        # 优雅处理整个批次无效的情况
        if V_padded is None: 
            return None
        
        total_loss, num_steps = 0.0, 0
        
        # 正确处理批处理数据
        for i in range(V_padded.shape[0]):  # 遍历批次中的每个序列
            V_seq, y_seq = V_padded[i, :lengths[i]], y_padded[i, :lengths[i]]
            
            for k in range(len(V_seq) - 1):  # 遍历序列中的每一帧
                V_k, n_k = V_seq[k], y_seq[k].item()
                n_k_plus_1 = y_seq[k+1]
                
                # 计算差分特征
                diff = torch.abs(V_k - self.g.proto[n_k])
                
                # 通过MLP预测权重调整量
                dW_k = self.diff_mlp(diff)
                
                # 计算下一步的logits（非累积更新）
                logits_k = self.g.get_next_step_logits(n_k, dW_k)
                
                # 计算交叉熵损失
                loss_k = F.cross_entropy(logits_k.unsqueeze(0), n_k_plus_1.unsqueeze(0))
                total_loss += loss_k
                num_steps += 1
        
        # 计算平均损失
        avg_loss = total_loss / num_steps if num_steps > 0 else torch.tensor(0.0, device=self.device, requires_grad=True)
        
        # 记录训练损失
        self.log("train_loss", avg_loss, batch_size=V_padded.shape[0], sync_dist=True)
        return avg_loss

    def validation_step(self, batch, batch_idx):
        """验证步骤"""
        V_padded, y_padded, lengths = batch
        if V_padded is None: 
            return None

        predictions, ground_truths = [], []
        
        for i in range(V_padded.shape[0]):
            self.g.reset()  # 每个序列开始前重置图状态
            V_seq, y_seq = V_padded[i, :lengths[i]], y_padded[i, :lengths[i]]
            
            # 预测时，我们假设前一帧的分类是正确的（teacher forcing）
            pred_frame_seq = [y_seq[0].item()]
            
            for k in range(len(V_seq) - 1):
                current_true_node = y_seq[k].item()
                V_k = V_seq[k]
                
                # 计算差分特征
                diff = torch.abs(V_k - self.g.proto[current_true_node])
                dW_k = self.diff_mlp(diff)
                
                # 更新累积图
                self.g.step_and_update(current_true_node, dW_k)
                
                # 从更新后的图中预测下一步
                next_step_logits = self.g.W[current_true_node]
                predicted_next_node = torch.argmax(next_step_logits).item()
                pred_frame_seq.append(predicted_next_node)
            
            predictions.append(pred_frame_seq)
            ground_truths.append(y_seq.tolist())

        output = {"preds": predictions, "gts": ground_truths}
        self.validation_step_outputs.append(output)
        return output

    def on_validation_epoch_end(self):
        """验证轮次结束时的处理"""
        # 导入评估指标
        try:
            from ..utils.metrics import greedy_merge, iou_edit, lev_norm
        except ImportError:
            # 如果还没有实现metrics模块，暂时跳过评估
            self.validation_step_outputs.clear()
            return

        # 使用收集的验证步骤输出
        outputs = self.validation_step_outputs
        if not outputs:
            return

        all_preds, all_gts = [], []

        for out in outputs:
            if out and isinstance(out, dict):
                all_preds.extend(out.get("preds", []))
                all_gts.extend(out.get("gts", []))

        if not all_preds:
            self.validation_step_outputs.clear()
            return

        iou_scores, lev_scores = [], []
        for pred_frames, gt_frames in zip(all_preds, all_gts):
            pred_segments = greedy_merge(pred_frames)
            gt_segments = greedy_merge(gt_frames)
            iou_scores.append(iou_edit(pred_segments, gt_segments))
            lev_scores.append(lev_norm(pred_segments, gt_segments))

        avg_iou = torch.tensor(iou_scores).mean() if iou_scores else 0.0
        avg_lev = torch.tensor(lev_scores).mean() if lev_scores else 0.0

        self.log_dict({"val_iou": avg_iou, "val_lev_norm": avg_lev}, prog_bar=True, sync_dist=True)

        # 清空输出列表
        self.validation_step_outputs.clear()

    def configure_optimizers(self):
        """配置优化器"""
        # 优化器只更新 diff_mlp 的参数，TaskGraph.W0 是固定的
        return torch.optim.Adam(self.diff_mlp.parameters(), lr=self.lr)
