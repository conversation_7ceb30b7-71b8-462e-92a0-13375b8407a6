import pytorch_lightning as pl
import torch
import numpy as np
from glob import glob
from torch.utils.data import Dataset, DataLoader
import os
import logging
from typing import Optional, Tuple, Dict, List

# 设置日志
logger = logging.getLogger(__name__)


def custom_collate_fn(batch):
    """
    自定义批处理函数，处理可变长度序列和无效样本

    Args:
        batch: 批次数据，包含(V_seq, y_seq)元组或None值

    Returns:
        tuple: (V_padded, y_padded, lengths) 或 (None, None, None)
    """
    # 过滤掉数据集中返回None的无效样本
    batch = [item for item in batch if item is not None]
    if not batch:
        return None, None, None  # 如果整个批次都无效，返回None
    
    V_seqs, y_seqs = zip(*batch)
    max_len = max(len(y) for y in y_seqs)
    D = V_seqs[0].shape[1]
    
    V_padded = torch.zeros(len(V_seqs), max_len, D, dtype=torch.float32)
    y_padded = torch.full((len(y_seqs), max_len), -100, dtype=torch.long)  # -100是CrossEntropyLoss的忽略索引
    lengths = torch.tensor([len(y) for y in y_seqs], dtype=torch.long)

    for i, (v, y) in enumerate(zip(V_seqs, y_seqs)):
        len_i = len(y)
        V_padded[i, :len_i, :] = v
        y_padded[i, :len_i] = y
    return V_padded, y_padded, lengths


class CerealsDataset(Dataset):
    """
    Breakfast数据集中cereals任务的数据集类
    支持从.npy特征文件和.txt标签文件加载数据
    """
    def __init__(self, root: str, split: str, action_map: dict):
        self.root = root
        self.split = split
        self.action_map = action_map
        self.pairs = self._find_files()

    def _find_files(self):
        """查找特征文件和对应的标签文件"""
        pairs = []
        subjects = ("s1", "s2", "s3") if self.split == "train" else ("s4",)
        
        for subj in subjects:
            # 查找特征文件（支持.npy和.txt格式）
            feature_pattern = f"{self.root}/breakfast_data/{subj}/cereals/*.npy"
            feature_files = glob(feature_pattern)
            
            # 如果没有.npy文件，尝试.txt文件
            if not feature_files:
                feature_pattern = f"{self.root}/breakfast_data/{subj}/cereals/*.txt"
                feature_files = glob(feature_pattern)
            
            for feature_file in feature_files:
                # 构建对应的标签文件路径
                base_name = os.path.basename(feature_file)
                if feature_file.endswith('.npy'):
                    label_name = base_name.replace('.npy', '.txt')
                else:
                    label_name = base_name
                
                # 根据Agent.md规范，标签文件路径格式
                label_file = os.path.join(
                    self.root,
                    "segmentation_coarse",
                    subj,
                    "cereals",
                    label_name
                )
                
                if os.path.exists(label_file):
                    pairs.append((feature_file, label_file))
        
        return pairs

    def __len__(self):
        return len(self.pairs)

    def __getitem__(self, i):
        """加载单个样本"""
        try:
            feature_file, label_file = self.pairs[i]
            
            # 加载特征
            if feature_file.endswith('.npy'):
                V = torch.from_numpy(np.load(feature_file)).float()
            else:
                # 如果是.txt文件，假设每行是一个特征向量
                V = torch.from_numpy(np.loadtxt(feature_file)).float()
                if V.dim() == 1:
                    V = V.unsqueeze(0)  # 确保是2D张量
            
            # 加载标签（原始标签是1-based，需要用action_map转换为0-based）
            # 根据Agent.md，标签文件格式为每行一个整数标签
            with open(label_file, 'r') as f:
                lines = f.readlines()

            # 解析标签，支持多种格式
            y_raw = []
            for line in lines:
                line = line.strip()
                if line:
                    # 如果是多列格式，取最后一列作为标签
                    parts = line.split()
                    if len(parts) >= 2:
                        y_raw.append(int(parts[-1]))  # 取最后一列
                    else:
                        y_raw.append(int(parts[0]))   # 单列格式

            y_raw = np.array(y_raw, dtype=np.int32)
            y = torch.tensor([self.action_map.get(str(label), -1) for label in y_raw], dtype=torch.long)
            
            # 验证特征和标签的长度匹配
            if len(V) != len(y):
                print(f"Warning: Feature length {len(V)} != label length {len(y)} in {feature_file}")
                min_len = min(len(V), len(y))
                V, y = V[:min_len], y[:min_len]

            # 过滤掉未映射的标签（如背景）
            valid_indices = y != -1
            V_valid, y_valid = V[valid_indices], y[valid_indices]

            # 如果过滤后序列为空或太短，则返回None，由collate_fn处理
            return (V_valid, y_valid) if len(y_valid) > 1 else None
            
        except Exception as e:
            # 文件损坏或格式错误时，返回None
            logger.warning(f"Error loading {self.pairs[i]}: {e}")
            return None


class BreakfastDataModule(pl.LightningDataModule):
    """
    PyTorch Lightning数据模块，管理训练、验证和测试数据集
    """
    def __init__(self, data_root: str, batch_size: int, action_map: dict, num_workers: int = 4):
        super().__init__()
        self.data_root = data_root
        self.batch_size = batch_size
        self.action_map = action_map
        self.num_workers = num_workers

    def setup(self, stage: str):
        """设置数据集"""
        if stage == 'fit' or stage is None:
            self.train_dataset = CerealsDataset(self.data_root, "train", self.action_map)
            self.val_dataset = CerealsDataset(self.data_root, "test", self.action_map)
        if stage == 'test' or stage is None:
            self.test_dataset = CerealsDataset(self.data_root, "test", self.action_map)

    def train_dataloader(self):
        return DataLoader(
            self.train_dataset, 
            self.batch_size, 
            shuffle=True, 
            collate_fn=custom_collate_fn, 
            num_workers=self.num_workers
        )

    def val_dataloader(self):
        return DataLoader(
            self.val_dataset, 
            self.batch_size, 
            collate_fn=custom_collate_fn, 
            num_workers=self.num_workers
        )

    def test_dataloader(self):
        return DataLoader(
            self.test_dataset, 
            self.batch_size, 
            collate_fn=custom_collate_fn, 
            num_workers=self.num_workers
        )
