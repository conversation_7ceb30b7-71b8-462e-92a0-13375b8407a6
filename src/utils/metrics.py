"""
评估指标模块

提供段级序列评估的IoU和编辑距离指标
"""

from typing import List
import warnings

# 尝试导入Levenshtein库
try:
    import Levenshtein
    HAS_LEVENSHTEIN = True
except ImportError:
    HAS_LEVENSHTEIN = False
    warnings.warn("python-Levenshtein not installed. Using fallback implementation for edit distance.")


def greedy_merge(frame_seq: List[int]) -> List[int]:
    """
    将帧级序列合并为段级序列
    
    Args:
        frame_seq: 帧级动作序列
        
    Returns:
        段级动作序列（连续相同的动作被合并为一个段）
    """
    if not frame_seq: 
        return []
    
    segment_seq = [frame_seq[0]]
    for i in range(1, len(frame_seq)):
        if frame_seq[i] != frame_seq[i-1]:
            segment_seq.append(frame_seq[i])
    return segment_seq


def iou_edit(pred_seq: List[int], gt_seq: List[int]) -> float:
    """
    计算基于段集合的IoU（交并比）
    
    注意：此指标衡量的是出现了哪些动作类型，不关心顺序和次数
    
    Args:
        pred_seq: 预测的段级序列
        gt_seq: 真实的段级序列
        
    Returns:
        IoU分数，范围[0, 1]
    """
    pred_segments = set(pred_seq)
    gt_segments = set(gt_seq)
    
    intersection = len(pred_segments.intersection(gt_segments))
    union = len(pred_segments.union(gt_segments))
    
    return intersection / union if union > 0 else 0.0


def _levenshtein_fallback(s1: str, s2: str) -> int:
    """
    编辑距离的回退实现（当python-Levenshtein不可用时）
    """
    if len(s1) < len(s2):
        return _levenshtein_fallback(s2, s1)

    if len(s2) == 0:
        return len(s1)

    previous_row = list(range(len(s2) + 1))
    for i, c1 in enumerate(s1):
        current_row = [i + 1]
        for j, c2 in enumerate(s2):
            insertions = previous_row[j + 1] + 1
            deletions = current_row[j] + 1
            substitutions = previous_row[j] + (c1 != c2)
            current_row.append(min(insertions, deletions, substitutions))
        previous_row = current_row
    
    return previous_row[-1]


def lev_norm(pred_seq: List[int], gt_seq: List[int]) -> float:
    """
    计算归一化的编辑距离（Levenshtein distance）

    直接对整数序列计算编辑距离，避免字符转换的复杂性

    Args:
        pred_seq: 预测的段级序列
        gt_seq: 真实的段级序列

    Returns:
        归一化编辑距离，范围[0, 1]
    """
    if not pred_seq and not gt_seq:
        return 0.0

    # 直接对整数列表计算编辑距离
    if HAS_LEVENSHTEIN:
        # python-Levenshtein可以直接处理序列
        lev_dist = Levenshtein.distance(pred_seq, gt_seq)
    else:
        # 使用回退实现，将整数转换为字符串避免Unicode问题
        str_pred = ",".join(map(str, pred_seq))
        str_gt = ",".join(map(str, gt_seq))
        lev_dist = _levenshtein_fallback(str_pred, str_gt)

    max_len = max(len(pred_seq), len(gt_seq))
    return lev_dist / max_len if max_len > 0 else 0.0


def compute_metrics(pred_sequences: List[List[int]], gt_sequences: List[List[int]]) -> dict:
    """
    批量计算评估指标
    
    Args:
        pred_sequences: 预测序列列表
        gt_sequences: 真实序列列表
        
    Returns:
        包含平均IoU和编辑距离的字典
    """
    if len(pred_sequences) != len(gt_sequences):
        raise ValueError("Prediction and ground truth sequences must have the same length")
    
    iou_scores = []
    lev_scores = []
    
    for pred_frames, gt_frames in zip(pred_sequences, gt_sequences):
        # 转换为段级序列
        pred_segments = greedy_merge(pred_frames)
        gt_segments = greedy_merge(gt_frames)
        
        # 计算指标
        iou_scores.append(iou_edit(pred_segments, gt_segments))
        lev_scores.append(lev_norm(pred_segments, gt_segments))
    
    return {
        'iou_mean': sum(iou_scores) / len(iou_scores) if iou_scores else 0.0,
        'lev_mean': sum(lev_scores) / len(lev_scores) if lev_scores else 0.0,
        'iou_scores': iou_scores,
        'lev_scores': lev_scores
    }
