seed: 42
task: cereals
data_root: /data2/syd_data/Breakfast_Data
output_dir: ${oc.env:OUTPUT_DIR,./outputs}
stats_dir: ./stats
model:
  D: 64
  M: 48
  H: 256
  alpha: 0.05
  lr: 0.0001
trainer:
  max_epochs: 50
  batch_size: 16
  num_workers: 4
  accelerator: auto
  devices: 1
  gradient_clip_val: 1.0
  accumulate_grad_batches: 1
  val_check_interval: 1.0
  check_val_every_n_epoch: 1
data:
  normalize: false
  augmentation: false
  min_sequence_length: 2
  max_sequence_length: 1000
logging:
  log_every_n_steps: 50
  save_top_k: 3
  monitor: val_iou
  mode: max
experiment:
  name: cereals_dynamic_graph
  version: null
  tags:
  - cereals
  - dynamic_graph
  - breakfast
debug:
  fast_dev_run: true
  overfit_batches: 0
  limit_train_batches: 1.0
  limit_val_batches: 1.0
