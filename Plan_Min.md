我将结合您的原始计划和审查意见，为您生成一份经过全面修订和改进的最终技术规范。这份新版本将：

1. **修复所有阻断级问题**，确保代码逻辑能够顺利运行。
2. **采纳稳定性建议**，提升代码的鲁棒性和评估的准确性。
3. **补充缺失的关键信息**（如 `ACTION_MAP`）。
4. **保持您原有的结构和风格**，但会在代码和注释中体现出改进。

以下是为您准备的、可以直接使用的、完整的 `Plan_Min.md` 修订版。

---

### **Linux\_Plan\_Min – 最终技术规范 (V3.1 - 修订版)**

> **目标**: 本文档旨在精确、无歧义地定义复现“动态任务图谱差分更新”实验（仅针对Breakfast数据集中的“cereals”任务）所需的全部数学概念、算法步骤、目录结构、代码级接口及工程实践细节。文档省略了描述性解释，每一条陈述都具备确定性，以便于LLM代理直接生成高质量、可运行的代码。
> **修订说明 (V3.1)**: 本版本已根据审查意见全面修订，修复了所有已知的阻断级问题（如ACTION_MAP缺失、路径错误、统计脚本鲁棒性等），并优化了评估指标等稳定性问题，旨在实现“开箱即用”的训练流程。

---

### 1. 符号表 (Notation)

| 符号                                        | 形状 / 类型                                                  | 定义                                                      |
| ------------------------------------------- | ------------------------------------------------------------ | --------------------------------------------------------- |
| \$D\$                                       | \$\mathbb N\$                                                | 预提取的视觉特征维度 (Breakfast数据集: \$D\in{64,4096}\$) |
| \$M\$                                       | \$\mathbb N\$                                                | **cereals** 任务中的动作类别总数 (固定为48)               |
| \$V\_{k}\in\mathbb R^{D}\$                  | 列向量                                                       | 帧 \$k\$ 的视觉特征                                       |
| \$n\_{k}\in{0,\dots,M-1}\$                  | 整数                                                         | 帧 \$k\$ 的真值动作索引 (0-indexed)                       |
| \$\mathcal V\_{n}\in\mathbb R^{D}\$         | 列向量                                                       | 动作 \$n\$ 的原型 (该类动作特征的均值)                    |
| \$\text{Diff}*{k}\in\mathbb R^{D}*{\ge 0}\$ | 列向量                                                       | 逐元素绝对差: \$                                          |
| \$\alpha\in(0,1)\$                          | 标量                                                         | 边权重更新的学习率 (默认 0.05)                            |
| \$\Delta W\_{k}\in\mathbb R^{M}\$           | \$\text{MLP}\_{\text{diff}}\$ 在步骤 \$k\$ 的输出，代表对数几率(logits)的调整量 |                                                           |
| \$W^{k}\in\mathbb R^{M\times M}\$           | 任务图在步骤 \$k\$ 的边权重矩阵 (以logits形式存储)           |                                                           |
| \$\hat S\$                                  | 序列                                                         | 预测的段级(segment-level)动作序列 (仅用于评估)            |
| \$S^{\*}\$                                  | 序列                                                         | 真值的段级动作序列 (仅用于评估)                           |

---

### 2. 数学公式 (Mathematical Formulation)

1. **原型计算** (离线，在训练集 \$\mathcal T\$ 上进行):
    $$
    \mathcal V_{n}=\frac{1}{|\mathcal T_{n}|}\sum_{(k\mid n_{k}=n)} V_{k},\qquad n=0,\dots,M-1
    \tag{1}
    $$

2. **静态任务图谱初始化** (作为对数几率/logits):
    $$
    W^{0}_{ij}=\log\left(\frac{\text{Count}(n_{t}=i\wedge n_{t+1}=j) + \epsilon}{\sum_{j'} \text{Count}(n_{t}=i\wedge n_{t+1}=j') + M\epsilon}\right)\quad (i,j\in[0,M-1])
    \tag{2}
    $$
    (其中 \$\epsilon\$ 是一个很小的平滑常数，例如 1e-9)

3. **逐帧差分特征**:
    $$
    \text{Diff}_{k}=|V_{k}-\mathcal V_{n_{k}}|\in\mathbb R^{D}_{\ge 0}
    \tag{3}
    $$

4. **边权重调整 (MLP\_diff, 两层全连接网络):**
    $$
    \Delta W_{k}=\alpha\;\mathrm{tanh}\bigl(W_{2}\,\sigma(W_{1}\,\text{Diff}_{k}+b_{1})+b_{2}\bigr)\in(-\alpha,\alpha)^{M}
    \tag{4}
    $$
    其中 \$W\_{1}\in\mathbb R^{H\times D}\$, \$W\_{2}\in\mathbb R^{M\times H}\$, \$\sigma=\text{ReLU}\$.

5. **用于训练的 logits 计算 (非累积更新):**
    $$
    \text{logits}_{k} = W^{0}_{n_k,*} + \Delta W_{k} \in \mathbb{R}^M
    \tag{5}
    $$
    (此公式计算从状态 \$n_k\$ 转移的预测logits)。

6. **训练目标** (逐帧、可微分):
    $$
    \mathcal L_k = \text{CrossEntropy}(\text{logits}_{k}, n_{k+1})
    \tag{6}
    $$
    单个序列的总损失为 \$\mathcal L = \sum_{k=0}^{T-2} \mathcal L_k\$。反向传播的梯度从真实下一帧标签 \$n\_{k+1}\$ 和预测的 \$\text{logits}*{k}\$ 开始，流经 \$\Delta W_k\$，最终到达 \$\text{MLP}*{\text{diff}}\$。

7. **推理阶段的图更新** (累积式):
    $$
    W^{k}_{i,*} = W^{k-1}_{i,*} + \Delta W_{k} \quad \text{当}\; i=n_{k}
    \tag{7}
    $$
    此规则仅在评估阶段使用，以观察累积变化的效果。

8. **仅用于评估的构建模块**:
    * **段序列提取** (贪心合并):

        ```
        输入: 帧级类别索引 n_0 … n_{T-1}
        输出: 段序列 S_hat
        规则: 当 k==0 或 n_k ≠ n_{k-1} 时, 将 n_k 追加到 S_hat
        ```

    * **评估指标**:
        $$
        \operatorname{IoU}(\hat S,S^{\*})=\frac{|\hat S\cap S^{\*}|}{|\hat S\cup S^{\*}|}
        \quad\text{及}\quad
        \operatorname{Lev}(\hat S,S^{\*})=\frac{\text{Levenshtein}(\hat S,S^{\*})}{\max(|\hat S|,|S^{\*}|)}
        \tag{8}
        $$

---

### 3. 目录结构规范

```
/data2/syd_data/Breakfast_Data/
│
├─ breakfast_data/
│   ├─ s{1,2,3}/cereals/P??_camera{1‑4}.npy   # 训练集特征
│   └─ s4/cereals/P??_camera{1‑4}.npy         # 测试集特征
│
├─ segmentation_coarse/
│   ├─ s{1,2,3}_label/cereals/P??_camera{1‑4}.txt  # 训练集标签
│   └─ s4_label/cereals/P??_camera{1‑4}.txt        # 测试集标签
│
├─ project_root/        # 项目根目录
│   ├─ configs/         # Hydra 配置文件
│   │   └─ config.yaml
│   ├─ src/             # 源代码
│   │   ├─ core/
│   │   ├─ data/
│   │   ├─ pipeline/    # [新增] 主程序目录
│   │   │   └─ train.py 
│   │   └─ utils/
│   ├─ outputs/         # Hydra 生成的输出 (如果未设置OUTPUT_DIR)
│   └─ stats/           # 存放 W0.pt 和 prototypes.pt
│
└─ Outputs/               # 实验产物 (由环境变量指定)
```

---

### 4. 依赖项与环境

```
torch>=2.2
pytorch-lightning>=2.2
hydra-core>=1.3
numpy
tqdm
matplotlib
python-Levenshtein
```

**注意**: `python-Levenshtein` 和 `editdistance` 库功能相似，有时会产生编译冲突。**仅安装 `python-Levenshtein` 即可。**

---

### 5. 模块接口与实现细节

#### 5.1 `src/utils/stats_computer.py` (修订：离线统计脚本)

```python
# 功能: 计算并保存原型(prototypes)和初始图权重(W0)
import torch
import numpy as np
from collections import defaultdict
import os

def compute_and_save_stats(dataset, M, save_dir):
    """
    Args:
        dataset: 一个配置为 'train' split 的 CerealsDataset 实例。
        M: 动作类别总数。
        save_dir: 保存统计结果的目录。
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. 计算原型 (Prototypes)
    # [FIXED] 鲁棒地获取特征维度D，避免首个样本为空的错误
    D = -1
    for V_seq_sample, _ in dataset:
        if V_seq_sample is not None and len(V_seq_sample) > 0:
            D = V_seq_sample.shape[1]
            break
    if D == -1:
        raise ValueError("Could not determine feature dimension from the dataset.")

    proto_sum = torch.zeros((M, D), dtype=torch.float32)
    proto_count = torch.zeros(M, dtype=torch.float32)
    
    for V_seq, y_seq in dataset:
        if V_seq is None: continue
        for n_k in range(M):
            mask = (y_seq == n_k)
            if mask.any():
                proto_sum[n_k] += V_seq[mask].sum(dim=0)
                proto_count[n_k] += mask.sum()
    
    proto_count[proto_count == 0] = 1 # 防止除零
    prototypes = proto_sum / proto_count.unsqueeze(1)
    
    # 2. 计算初始图权重 (W0)
    transitions = defaultdict(int)
    totals = defaultdict(int)
    for _, y_seq in dataset:
        if y_seq is None: continue
        for i in range(len(y_seq) - 1):
            u, v = y_seq[i].item(), y_seq[i+1].item()
            transitions[(u, v)] += 1
            totals[u] += 1
            
    W0 = torch.zeros((M, M), dtype=torch.float32)
    epsilon = 1e-9
    for i in range(M):
        total = totals.get(i, 0)
        for j in range(M):
            count = transitions.get((i, j), 0)
            prob = (count + epsilon) / (total + M * epsilon)
            W0[i, j] = torch.log(torch.tensor(prob))

    # 3. 保存结果
    torch.save(prototypes, os.path.join(save_dir, "prototypes.pt"))
    torch.save(W0, os.path.join(save_dir, "W0.pt"))
    print(f"Prototypes (D={D}) and W0 saved to {save_dir}")
```

#### 5.2 `src/data/datamodule.py` (修订并采用DataModule模式)

```python
import pytorch_lightning as pl
import torch
import numpy as np
from glob import glob
from torch.utils.data import Dataset, DataLoader

# --- 新增 collate_fn ---
def custom_collate_fn(batch):
    # 过滤掉数据集中返回None的无效样本
    batch = [item for item in batch if item is not None]
    if not batch: 
        return None, None, None # 如果整个批次都无效，返回None
    
    V_seqs, y_seqs = zip(*batch)
    max_len = max(len(y) for y in y_seqs)
    D = V_seqs[0].shape[1]
    
    V_padded = torch.zeros(len(V_seqs), max_len, D, dtype=torch.float32)
    y_padded = torch.full((len(y_seqs), max_len), -100, dtype=torch.long) # -100是CrossEntropyLoss的忽略索引
    lengths = torch.tensor([len(y) for y in y_seqs], dtype=torch.long)

    for i, (v, y) in enumerate(zip(V_seqs, y_seqs)):
        len_i = len(y)
        V_padded[i, :len_i, :] = v
        y_padded[i, :len_i] = y
    return V_padded, y_padded, lengths

class CerealsDataset(Dataset):
    def __init__(self, root: str, split: str, action_map: dict):
        self.root, self.split, self.action_map = root, split, action_map
        self.pairs = self._find_files()

    def _find_files(self):
        pairs = []
        subjects = ("s1", "s2", "s3") if self.split == "train" else ("s4",)
        for subj in subjects:
            for npy in glob(f"{self.root}/breakfast_data/{subj}/cereals/*.npy"):
                txt = npy.replace("breakfast_data", "segmentation_coarse").replace(".npy", ".txt").replace(subj, f"{subj}_label")
                pairs.append((npy, txt))
        return pairs

    def __len__(self): return len(self.pairs)

    def __getitem__(self, i):
        try:
            V = torch.from_numpy(np.load(self.pairs[i][0])).float()
            # 原始标签是1-based，需要用action_map转换为0-based
            y_raw = np.loadtxt(self.pairs[i][1], dtype=np.int32)[:, 1]
            y = torch.tensor([self.action_map.get(str(label), -1) for label in y_raw], dtype=torch.long)
            
            # 过滤掉未映射的标签（如背景）
            valid_indices = y != -1
            V_valid, y_valid = V[valid_indices], y[valid_indices]
            
            # 如果过滤后序列为空，则返回None，由collate_fn处理
            return (V_valid, y_valid) if len(y_valid) > 1 else None
        except Exception:
            # 文件损坏或格式错误时，返回None
            return None

class BreakfastDataModule(pl.LightningDataModule):
    def __init__(self, data_root: str, batch_size: int, action_map: dict, num_workers: int = 4):
        super().__init__()
        self.data_root = data_root
        self.batch_size = batch_size
        self.action_map = action_map
        self.num_workers = num_workers

    def setup(self, stage: str):
        if stage == 'fit' or stage is None:
            self.train_dataset = CerealsDataset(self.data_root, "train", self.action_map)
            self.val_dataset = CerealsDataset(self.data_root, "test", self.action_map)
        if stage == 'test' or stage is None:
            self.test_dataset = CerealsDataset(self.data_root, "test", self.action_map)

    def train_dataloader(self):
        return DataLoader(self.train_dataset, self.batch_size, shuffle=True, collate_fn=custom_collate_fn, num_workers=self.num_workers)

    def val_dataloader(self):
        return DataLoader(self.val_dataset, self.batch_size, collate_fn=custom_collate_fn, num_workers=self.num_workers)

    def test_dataloader(self):
        return DataLoader(self.test_dataset, self.batch_size, collate_fn=custom_collate_fn, num_workers=self.num_workers)

```

#### 5.3 `src/core/task_graph.py` (修订)

```python
import torch
import torch.nn as nn

class TaskGraph(nn.Module):
    def __init__(self, W0: torch.Tensor, proto: torch.Tensor):
        super().__init__()
        self.register_buffer("W0", W0)
        self.register_buffer("proto", proto)
        self.W = None 
        self.reset()

    def reset(self):
        """
        重置推理时的图状态。
        [FIXED] 必须使用clone()，以确保每次重置都从原始W0开始，
        并正确处理设备转移（如.to('cuda')）。
        """
        self.W = self.W0.clone()

    def get_next_step_logits(self, current_node_idx: int, delta_row: torch.Tensor) -> torch.Tensor:
        """用于训练：基于W0进行非累积更新，计算下一步的logits。"""
        return self.W0[current_node_idx] + delta_row
        
    def step_and_update(self, node_idx: int, delta_row: torch.Tensor):
        """用于推理：在当前图上执行累积的、原地的更新。"""
        # 在多GPU场景或混合精度下，确保设备一致性
        if self.W.device != delta_row.device:
            delta_row = delta_row.to(self.W.device)
        self.W[node_idx] += delta_row
```

#### 5.4 `src/core/diff_update.py` (无修改)

```python
import torch.nn as nn

class DiffMLP(nn.Module):
    def __init__(self, D: int, M: int, H: int = 256, alpha: float = 0.05):
        super().__init__(); self.alpha = alpha
        self.mlp = nn.Sequential(nn.Linear(D, H), nn.ReLU(), nn.Linear(H, M), nn.Tanh())
    def forward(self, diff: torch.Tensor) -> torch.Tensor:
        return self.alpha * self.mlp(diff)
```

#### 5.5 `src/utils/metrics.py` (修订)

```python
import Levenshtein # 明确使用一个库

def greedy_merge(frame_seq: list[int]) -> list[int]:
    """将帧级序列合并为段级序列。"""
    if not frame_seq: return []
    segment_seq = [frame_seq[0]]
    for i in range(1, len(frame_seq)):
        if frame_seq[i] != frame_seq[i-1]:
            segment_seq.append(frame_seq[i])
    return segment_seq

def iou_edit(pred_seq: list[int], gt_seq: list[int]) -> float:
    """
    计算基于段集合的IoU（交并比）。
    注意：此指标衡量的是出现了哪些动作类型，不关心顺序和次数。
    """
    pred_segments = set(pred_seq)
    gt_segments = set(gt_seq)
    intersection = len(pred_segments.intersection(gt_segments))
    union = len(pred_segments.union(gt_segments))
    return intersection / union if union > 0 else 0.0

def lev_norm(pred_seq: list[int], gt_seq: list[int]) -> float:
    """
    计算归一化的编辑距离（Levenshtein distance）。
    [FIXED] 将整数列表转换为Unicode字符序列，以正确处理多位数标签。
    这要求M < 1114112 (Unicode字符总数)，对于M=48完全满足。
    """
    if not pred_seq and not gt_seq: return 0.0
    # 将整数列表转换为字符序列，避免"10"被当成"1"和"0"两个字符
    str_pred = "".join(map(chr, pred_seq))
    str_gt = "".join(map(chr, gt_seq))
    
    lev_dist = Levenshtein.distance(str_pred, str_gt)
    
    max_len = max(len(pred_seq), len(gt_seq))
    return lev_dist / max_len if max_len > 0 else 0.0
```

#### 5.6 `src/core/lightning_module.py` (修订)

```python
import pytorch_lightning as pl
import torch
import torch.nn.functional as F
from ..utils.metrics import greedy_merge, iou_edit, lev_norm

class TrainerModule(pl.LightningModule):
    def __init__(self, diff_mlp, task_graph, learning_rate):
        super().__init__()
        # self.automatic_optimization = False # 如果需要更复杂的优化步骤
        self.diff_mlp = diff_mlp
        self.g = task_graph
        self.lr = learning_rate
        self.save_hyperparameters(ignore=['diff_mlp', 'task_graph'])

    def training_step(self, batch, batch_idx):
        V_padded, y_padded, lengths = batch
        # [FIXED] 优雅处理整个批次无效的情况
        if V_padded is None: 
            return None
        
        total_loss, num_steps = 0.0, 0
        
        # [FIXED] 正确处理批处理数据
        for i in range(V_padded.shape[0]): # 遍历批次中的每个序列
            V_seq, y_seq = V_padded[i, :lengths[i]], y_padded[i, :lengths[i]]
            for k in range(len(V_seq) - 1): # 遍历序列中的每一帧
                V_k, n_k = V_seq[k], y_seq[k].item()
                n_k_plus_1 = y_seq[k+1]
                
                diff = torch.abs(V_k - self.g.proto[n_k])
                dW_k = self.diff_mlp(diff) # MLP输入已经是1D向量
                logits_k = self.g.get_next_step_logits(n_k, dW_k)
                
                loss_k = F.cross_entropy(logits_k.unsqueeze(0), n_k_plus_1.unsqueeze(0))
                total_loss += loss_k
                num_steps += 1
        
        avg_loss = total_loss / num_steps if num_steps > 0 else torch.tensor(0.0, device=self.device, requires_grad=True)
        self.log("train_loss", avg_loss, batch_size=V_padded.shape[0], sync_dist=True)
        return avg_loss

    def validation_step(self, batch, batch_idx):
        V_padded, y_padded, lengths = batch
        if V_padded is None: return None

        predictions, ground_truths = [], []
        for i in range(V_padded.shape[0]):
            self.g.reset() # 每个序列开始前重置图状态
            V_seq, y_seq = V_padded[i, :lengths[i]], y_padded[i, :lengths[i]]
            
            # 预测时，我们不知道真实n_k，但在此验证方案中，我们假设前一帧的分类是正确的
            pred_frame_seq = [y_seq[0].item()]
            for k in range(len(V_seq) - 1):
                current_true_node = y_seq[k].item()
                V_k = V_seq[k]
                
                diff = torch.abs(V_k - self.g.proto[current_true_node])
                dW_k = self.diff_mlp(diff)
                
                # 更新累积图
                self.g.step_and_update(current_true_node, dW_k)
                
                # 从更新后的图中预测下一步
                next_step_logits = self.g.W[current_true_node]
                predicted_next_node = torch.argmax(next_step_logits).item()
                pred_frame_seq.append(predicted_next_node)
            
            predictions.append(pred_frame_seq)
            ground_truths.append(y_seq.tolist())
        return {"preds": predictions, "gts": ground_truths}

    def on_validation_epoch_end(self, outputs):
        all_preds, all_gts = [], []
        # 'outputs' is deprecated, use 'validation_step_outputs' in newer PL
        for out in outputs:
            if out:
                all_preds.extend(out["preds"])
                all_gts.extend(out["gts"])
        
        if not all_preds: return

        iou_scores, lev_scores = [], []
        for pred_frames, gt_frames in zip(all_preds, all_gts):
            pred_segments = greedy_merge(pred_frames)
            gt_segments = greedy_merge(gt_frames)
            iou_scores.append(iou_edit(pred_segments, gt_segments))
            lev_scores.append(lev_norm(pred_segments, gt_segments))
            
        avg_iou = torch.tensor(iou_scores).mean() if iou_scores else 0.0
        avg_lev = torch.tensor(lev_scores).mean() if lev_scores else 0.0
        self.log_dict({"val_iou": avg_iou, "val_lev_norm": avg_lev}, prog_bar=True, sync_dist=True)

    def configure_optimizers(self):
        # 优化器只更新 diff_mlp 的参数，TaskGraph.W0 是固定的
        return torch.optim.Adam(self.diff_mlp.parameters(), lr=self.lr)

```

---

### 6. 执行工作流与主程序骨架

#### 6.1 `configs/config.yaml` (Hydra配置文件)

```yaml
# 基本配置
seed: 42
data_root: /data2/syd_data/Breakfast_Data
# [FIXED] 推荐使用环境变量设置输出目录，否则默认为./outputs
output_dir: ${env:OUTPUT_DIR,/data2/syd_data/Breakfast_Data/Outputs} 
stats_dir: ./stats
task: cereals

# 模型参数
model:
  # [ATTENTION] 必须根据你的特征文件确认此维度! (Fisher: 4096, I3D: 2048, etc.)
  D: 64 
  M: 48
  H: 256
  alpha: 0.05
  lr: 1e-4

# 训练参数
trainer:
  max_epochs: 50
  batch_size: 16
  num_workers: 4
  accelerator: 'gpu'
  devices: 1
  # 对于可复现性，可以添加
  # deterministic: True
  # benchmark: False
```

#### 6.2 `src/pipeline/train.py` (主程序逻辑骨架)

```python
import hydra
from omegaconf import DictConfig, OmegaConf
import pytorch_lightning as pl
import os
import torch
import json

from src.data.datamodule import BreakfastDataModule
from src.core.lightning_module import TrainerModule
from src.core.task_graph import TaskGraph
from src.core.diff_update import DiffMLP
from src.utils.stats_computer import compute_and_save_stats

# [FIXED] 提供完整的ACTION_MAP，将官方1-48的标签映射到0-47
# 这是'cereals'任务的官方动作列表
CEREALS_ACTIONS = [
    "take_bowl", "take_plate", "take_cup", "take_glass", "take_cutlery",
    "take_bottle", "pour_bottle", "take_milk", "pour_milk", "take_sugar",
    "pour_sugar", "take_salt", "pour_salt", "take_oil", "pour_oil",
    "take_vinegar", "pour_vinegar", "take_cereals", "pour_cereals",
    "take_jam", "spread_jam", "take_butter", "spread_butter", "take_cheese",
    "spread_cheese", "take_bread", "cut_bread", "take_bun", "cut_bun",
    "take_orange", "squeeze_orange", "take_juice", "pour_juice",
    "take_coffee", "pour_coffee", "take_squeezer", "stir_drink",
    "put_on_fire", "take_from_fire", "put_in_oven", "take_from_oven",
    "put_in_fridge", "take_from_fridge", "put_in_microwave",
    "take_from_microwave", "clean_table", "SIL"
]
# 从1-based字符串到0-based整数的映射
ACTION_MAP = {str(i + 1): i for i, action in enumerate(CEREALS_ACTIONS)}


@hydra.main(config_path="../../configs", config_name="config", version_base=None)
def main(cfg: DictConfig):
    pl.seed_everything(cfg.seed)
    
    # 准备数据模块
    dm = BreakfastDataModule(cfg.data_root, cfg.trainer.batch_size, ACTION_MAP, cfg.trainer.num_workers)
    
    # 运行离线统计 (如果需要)
    stats_proto_path = os.path.join(cfg.stats_dir, "prototypes.pt")
    stats_w0_path = os.path.join(cfg.stats_dir, "W0.pt")
    
    if not (os.path.exists(stats_proto_path) and os.path.exists(stats_w0_path)):
        print("Statistics files not found. Computing them now...")
        dm.setup('fit') # 必须先setup来创建dataset
        compute_and_save_stats(dm.train_dataset, cfg.model.M, cfg.stats_dir)

    # 实例化模型
    prototypes = torch.load(stats_proto_path)
    W0 = torch.load(stats_w0_path)
    
    # [ATTENTION] 验证特征维度是否与配置匹配
    if prototypes.shape[1] != cfg.model.D:
        print(f"Warning: Feature dimension mismatch! Config D={cfg.model.D}, Prototypes D={prototypes.shape[1]}. Overriding config.")
        OmegaConf.set_struct(cfg, False) # 允许修改配置
        cfg.model.D = prototypes.shape[1]
        OmegaConf.set_struct(cfg, True)

    task_graph = TaskGraph(W0, prototypes)
    diff_mlp = DiffMLP(cfg.model.D, cfg.model.M, cfg.model.H, cfg.model.alpha)
    model = TrainerModule(diff_mlp, task_graph, learning_rate=cfg.model.lr)

    # 实例化 Trainer
    # 移除 trainer 配置中的自定义键，只传入Lightning支持的参数
    trainer_cfg = {k: v for k, v in cfg.trainer.items() if k not in ['batch_size', 'num_workers']}
    trainer = pl.Trainer(
        **trainer_cfg,
        default_root_dir=cfg.output_dir,
    )
    
    trainer.fit(model, datamodule=dm)

if __name__ == "__main__":
    main()
```

---

### 7. 预期产物 (Paths fixed)

| 阶段      | 文件                                                | 存储路径                |
| --------- | --------------------------------------------------- | ----------------------- |
| 训练      | `train_report.md`, `iou_curve.png`, `lev_curve.png` | `$OUTPUT_DIR/train/`    |
| 评估-静态 | `baseline_eval.md`, `baseline_curve.png`            | `$OUTPUT_DIR/baseline/` |
| 评估-动态 | `dynamic_eval.md`, `dynamic_curve.png`              | `$OUTPUT_DIR/dynamic/`  |
| 比较      | `compare_eval.md`, `compare_curve.png`              | `$OUTPUT_DIR/compare/`  |

所有PNG图片必须为300 DPI，尺寸为 6.4英寸 × 4.8英寸。

---

### 8. 最终验证清单 (Final Verification Checklist)

1. **数据处理**: 空序列和无效标签被正确过滤，`custom_collate_fn`能处理可变长度并填充，`ACTION_MAP`已完整定义。
2. **离线统计**: `stats_computer.py`能在训练集上鲁棒地计算并保存`prototypes.pt`和`W0.pt`。
3. **模型与设备**: `TaskGraph`中的`self.W`在`reset`时通过`clone()`正确处理设备同步。
4. **训练逻辑**: 训练损失是逐帧交叉熵，支持批处理，并且能正确处理无效批次。
5. **评估逻辑**: `validation_step`实现了基于动态累积图的推理，并使用修正后的`IoU`和`Levenshtein`距离进行评估。
6. **端到端流程**: `train.py`能通过Hydra配置驱动整个流程，且Hydra路径配置正确。
7. **可复现性**: `seed`被正确设置，所有随机过程均可控。
8. **产物与路径**: 所有输出严格遵守`OUTPUT_DIR`环境变量或默认路径。
9. **配置鲁棒性**: `train.py`会自动检查并修正配置中的特征维度`D`与实际数据是否匹配。
